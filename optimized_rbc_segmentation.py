#!/usr/bin/env python3
"""
优化的红细胞分割算法
基于实际图像特征调整参数
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import os
import argparse

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Hiragino Sans GB']
plt.rcParams['axes.unicode_minus'] = False


class OptimizedRBCSegmentation:
    """优化的红细胞分割类"""
    
    def __init__(self, min_area: int = 300, max_area: int = 3000, 
                 min_circularity: float = 0.4):
        """
        初始化优化的分割器
        """
        self.min_area = min_area
        self.max_area = max_area
        self.min_circularity = min_circularity
        
    def smart_preprocessing(self, image: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """
        智能预处理
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 1. 背景估计和减除
        # 使用大核的中值滤波估计背景
        background = cv2.medianBlur(gray, 51)
        
        # 背景减除
        corrected = cv2.absdiff(gray, background)
        
        # 2. 对比度增强
        # 使用CLAHE增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(corrected)
        
        # 3. 轻微的高斯滤波
        smoothed = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        return smoothed, background
    
    def adaptive_segmentation(self, preprocessed_image: np.ndarray) -> Tuple[np.ndarray, List]:
        """
        自适应分割
        """
        # 1. 多种阈值方法组合
        # Otsu阈值
        _, binary_otsu = cv2.threshold(preprocessed_image, 0, 255, 
                                      cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 自适应阈值
        binary_adaptive = cv2.adaptiveThreshold(
            preprocessed_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 15, 5
        )
        
        # 组合两种方法
        binary_combined = cv2.bitwise_and(binary_otsu, binary_adaptive)
        
        # 2. 形态学操作
        # 去除小噪声
        kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        opened = cv2.morphologyEx(binary_combined, cv2.MORPH_OPEN, kernel_open, iterations=1)
        
        # 填充细胞内部
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel_close, iterations=2)
        
        # 3. 轮廓检测
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        return closed, contours
    
    def enhanced_filtering(self, contours: List, image_shape: Tuple) -> List:
        """
        增强的轮廓过滤
        """
        filtered_contours = []
        
        for contour in contours:
            # 基本几何特征
            area = cv2.contourArea(contour)
            
            # 面积过滤
            if not (self.min_area <= area <= self.max_area):
                continue
            
            # 轮廓点数检查
            if len(contour) < 5:
                continue
            
            # 周长和圆形度
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue
            
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity < self.min_circularity:
                continue
            
            # 边界框检查
            x, y, w, h = cv2.boundingRect(contour)
            
            # 排除接触边界的轮廓
            margin = 5
            if (x < margin or y < margin or 
                x + w > image_shape[1] - margin or 
                y + h > image_shape[0] - margin):
                continue
            
            # 长宽比检查
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio > 2.5:  # 放宽长宽比限制
                continue
            
            # 面积与边界框面积比
            bbox_area = w * h
            if bbox_area > 0:
                fill_ratio = area / bbox_area
                if fill_ratio < 0.5:  # 填充率太低可能是噪声
                    continue
            
            filtered_contours.append(contour)
        
        return filtered_contours
    
    def visualize_detailed_results(self, original_image: np.ndarray, 
                                 preprocessed: np.ndarray, background: np.ndarray,
                                 binary_mask: np.ndarray, contours: List, 
                                 save_path: Optional[str] = None) -> np.ndarray:
        """
        详细的结果可视化
        """
        result_image = original_image.copy()
        
        # 绘制轮廓和编号
        for i, contour in enumerate(contours):
            # 绘制轮廓
            cv2.drawContours(result_image, [contour], -1, (0, 255, 0), 2)
            
            # 计算质心并标注编号
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result_image, str(i+1), (cx-15, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 3)
                cv2.putText(result_image, str(i+1), (cx-15, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        
        # 添加统计信息
        cell_count = len(contours)
        cv2.putText(result_image, f"Red Blood Cells: {cell_count}", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 4)
        cv2.putText(result_image, f"Red Blood Cells: {cell_count}", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        
        if save_path:
            cv2.imwrite(save_path, result_image)
        
        # 创建处理过程可视化
        if save_path:
            base_path = os.path.splitext(save_path)[0]
            
            plt.figure(figsize=(20, 12))
            
            # 原始图像
            plt.subplot(2, 4, 1)
            plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
            plt.title("Original Image")
            plt.axis('off')
            
            # 背景估计
            plt.subplot(2, 4, 2)
            plt.imshow(background, cmap='gray')
            plt.title("Estimated Background")
            plt.axis('off')
            
            # 预处理结果
            plt.subplot(2, 4, 3)
            plt.imshow(preprocessed, cmap='gray')
            plt.title("Preprocessed")
            plt.axis('off')
            
            # 二值化掩码
            plt.subplot(2, 4, 4)
            plt.imshow(binary_mask, cmap='gray')
            plt.title("Binary Mask")
            plt.axis('off')
            
            # 轮廓叠加
            contour_overlay = original_image.copy()
            cv2.drawContours(contour_overlay, contours, -1, (0, 255, 0), 2)
            plt.subplot(2, 4, 5)
            plt.imshow(cv2.cvtColor(contour_overlay, cv2.COLOR_BGR2RGB))
            plt.title(f"Detected Contours ({len(contours)})")
            plt.axis('off')
            
            # 最终结果
            plt.subplot(2, 4, 6)
            plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
            plt.title(f"Final Result ({cell_count} cells)")
            plt.axis('off')
            
            # 面积分布
            if contours:
                areas = [cv2.contourArea(c) for c in contours]
                plt.subplot(2, 4, 7)
                plt.hist(areas, bins=15, alpha=0.7, edgecolor='black')
                plt.title("Cell Area Distribution")
                plt.xlabel("Area (pixels)")
                plt.ylabel("Count")
            
            # 圆形度分布
            if contours:
                circularities = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        circularities.append(circularity)
                
                plt.subplot(2, 4, 8)
                plt.hist(circularities, bins=15, alpha=0.7, edgecolor='black')
                plt.title("Circularity Distribution")
                plt.xlabel("Circularity")
                plt.ylabel("Count")
            
            plt.tight_layout()
            plt.savefig(f"{base_path}_process.png", dpi=150, bbox_inches='tight')
            plt.close()
            
        return result_image
    
    def process_image(self, image_path: str, output_dir: str = "optimized_output") -> dict:
        """
        处理单张图像的完整流程
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        print(f"\n处理图像: {image_path}")
        print(f"图像尺寸: {image.shape}")
        
        # 预处理
        preprocessed, background = self.smart_preprocessing(image)
        
        # 分割
        binary_mask, contours = self.adaptive_segmentation(preprocessed)
        
        # 过滤
        filtered_contours = self.enhanced_filtering(contours, image.shape[:2])
        
        print(f"初始轮廓数: {len(contours)}")
        print(f"过滤后轮廓数: {len(filtered_contours)}")
        
        # 可视化结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        result_image = self.visualize_detailed_results(
            image, preprocessed, background, binary_mask, filtered_contours,
            os.path.join(output_dir, f"{base_name}_optimized_result.jpg")
        )
        
        return {
            "original_image": image,
            "preprocessed_image": preprocessed,
            "background": background,
            "binary_mask": binary_mask,
            "contours": filtered_contours,
            "result_image": result_image,
            "cell_count": len(filtered_contours)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="优化的红细胞分割算法")
    parser.add_argument("--input", "-i", type=str, help="输入图像路径")
    parser.add_argument("--output", "-o", type=str, default="optimized_output", help="输出目录")
    parser.add_argument("--min_area", type=int, default=300, help="最小细胞面积")
    parser.add_argument("--max_area", type=int, default=3000, help="最大细胞面积")
    parser.add_argument("--show", action="store_true", help="显示结果")
    
    args = parser.parse_args()
    
    # 创建优化的分割器
    segmenter = OptimizedRBCSegmentation(
        min_area=args.min_area, 
        max_area=args.max_area
    )
    
    if args.input:
        if os.path.isfile(args.input):
            result = segmenter.process_image(args.input, args.output)
            print(f"\n检测到 {result['cell_count']} 个红细胞")
    else:
        # 处理当前目录下的所有BMP文件
        bmp_files = [f for f in os.listdir('.') if f.lower().endswith('.bmp')]
        if bmp_files:
            total_cells = 0
            for bmp_file in bmp_files:
                result = segmenter.process_image(bmp_file, args.output)
                cell_count = result['cell_count']
                total_cells += cell_count
                print(f"✓ {bmp_file}: {cell_count} 个红细胞")
            
            print(f"\n{'='*50}")
            print(f"总计检测到 {total_cells} 个红细胞")
            print(f"结果保存在: {args.output}/ 目录")
            print(f"{'='*50}")
        else:
            print("当前目录下没有找到BMP图像文件")


if __name__ == "__main__":
    main()
