# 红细胞分割算法

这是一个基于OpenCV的红细胞自动分割算法，专门用于处理显微镜下的血液细胞图像。

## 功能特点

- **自动预处理**: 包括噪声去除、对比度增强等
- **智能分割**: 使用自适应阈值和形态学操作
- **细胞过滤**: 基于面积和圆形度过滤，去除噪声和非细胞对象
- **结果可视化**: 自动标记检测到的细胞并生成统计信息
- **批量处理**: 支持处理多个图像文件

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 快速测试

运行测试脚本来处理当前目录下的所有BMP文件：

```bash
python test_segmentation.py
```

### 2. 处理单个图像

```bash
python red_blood_cell_segmentation.py -i your_image.bmp --show
```

### 3. 批量处理

```bash
python red_blood_cell_segmentation.py -o output_directory
```

### 4. 自定义参数

```bash
python red_blood_cell_segmentation.py -i image.bmp --min_area 50 --max_area 8000 --show
```

## 参数说明

- `--input, -i`: 输入图像路径
- `--output, -o`: 输出目录 (默认: "output")
- `--min_area`: 最小细胞面积阈值 (默认: 100)
- `--max_area`: 最大细胞面积阈值 (默认: 5000)
- `--show`: 显示处理结果

## 算法流程

1. **图像预处理**
   - 转换为灰度图像
   - 高斯滤波去噪
   - CLAHE对比度增强

2. **细胞分割**
   - 自适应阈值二值化
   - 形态学开运算去除噪声
   - 形态学闭运算填充空洞
   - 距离变换处理重叠细胞

3. **轮廓检测与过滤**
   - 检测所有轮廓
   - 基于面积过滤
   - 基于圆形度过滤

4. **结果输出**
   - 标记检测到的细胞
   - 生成统计信息
   - 保存分割结果

## 输出文件

- `*_segmented.jpg`: 标记了细胞轮廓和编号的结果图像
- `*_mask.jpg`: 二值化分割掩码
- `segmentation_process_visualization.png`: 处理过程可视化（测试模式）

## 算法参数调优

根据您的图像特点，可能需要调整以下参数：

- `min_area` 和 `max_area`: 根据细胞大小调整
- 自适应阈值参数: 在 `segment_cells` 方法中调整
- 形态学操作的核大小: 根据图像分辨率调整
- 圆形度阈值: 在 `filter_contours` 方法中调整

## 注意事项

- 算法针对红细胞的圆形特征进行了优化
- 建议输入图像具有良好的对比度
- 对于重叠严重的细胞，可能需要进一步优化分水岭算法
- 不同的显微镜和拍摄条件可能需要调整参数

## 示例结果

运行测试后，您将看到：
- 检测到的细胞数量
- 每个细胞的轮廓标记
- 处理过程的可视化展示

## 故障排除

1. **没有检测到细胞**: 尝试调整 `min_area` 和 `max_area` 参数
2. **检测到太多噪声**: 增加 `min_area` 或调整圆形度阈值
3. **细胞被分割**: 调整形态学操作的参数
4. **重叠细胞未分离**: 优化距离变换和分水岭算法参数
