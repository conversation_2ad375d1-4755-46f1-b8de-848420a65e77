#!/usr/bin/env python3
"""
红细胞分割算法
使用OpenCV和NumPy实现对显微镜图像中红细胞的自动分割
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import os
import argparse


class RedBloodCellSegmentation:
    """红细胞分割类"""
    
    def __init__(self, min_area: int = 100, max_area: int = 5000):
        """
        初始化分割器
        
        Args:
            min_area: 最小细胞面积阈值
            max_area: 最大细胞面积阈值
        """
        self.min_area = min_area
        self.max_area = max_area
        
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入的BGR图像
            
        Returns:
            预处理后的灰度图像
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯滤波去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 对比度增强 - CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(blurred)
        
        return enhanced
    
    def segment_cells(self, preprocessed_image: np.ndarray) -> Tuple[np.ndarray, List]:
        """
        细胞分割核心算法
        
        Args:
            preprocessed_image: 预处理后的灰度图像
            
        Returns:
            二值化掩码和轮廓列表
        """
        # 自适应阈值分割
        binary = cv2.adaptiveThreshold(
            preprocessed_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # 形态学操作 - 开运算去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        opened = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 形态学操作 - 闭运算填充细胞内部空洞
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel_close, iterations=2)
        
        # 距离变换和分水岭算法分离重叠细胞
        dist_transform = cv2.distanceTransform(closed, cv2.DIST_L2, 5)
        _, sure_fg = cv2.threshold(dist_transform, 0.4 * dist_transform.max(), 255, 0)
        sure_fg = np.uint8(sure_fg)
        
        # 查找轮廓
        contours, _ = cv2.findContours(sure_fg, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        return closed, contours
    
    def filter_contours(self, contours: List) -> List:
        """
        过滤轮廓，保留符合红细胞特征的轮廓
        
        Args:
            contours: 输入轮廓列表
            
        Returns:
            过滤后的轮廓列表
        """
        filtered_contours = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # 面积过滤
            if self.min_area <= area <= self.max_area:
                # 计算圆形度 (4π*面积/周长²)
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    
                    # 红细胞通常比较圆，圆形度应该在合理范围内
                    if circularity > 0.3:  # 可以根据实际情况调整
                        filtered_contours.append(contour)
        
        return filtered_contours
    
    def visualize_results(self, original_image: np.ndarray, contours: List, 
                         save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化分割结果
        
        Args:
            original_image: 原始图像
            contours: 检测到的细胞轮廓
            save_path: 保存路径（可选）
            
        Returns:
            标注后的图像
        """
        result_image = original_image.copy()
        
        # 绘制轮廓和编号
        for i, contour in enumerate(contours):
            # 绘制轮廓
            cv2.drawContours(result_image, [contour], -1, (0, 255, 0), 2)
            
            # 计算质心并标注编号
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result_image, str(i+1), (cx-10, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        # 添加统计信息
        cell_count = len(contours)
        cv2.putText(result_image, f"Cells detected: {cell_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        if save_path:
            cv2.imwrite(save_path, result_image)
            
        return result_image
    
    def process_image(self, image_path: str, output_dir: str = "output") -> dict:
        """
        处理单张图像的完整流程
        
        Args:
            image_path: 输入图像路径
            output_dir: 输出目录
            
        Returns:
            包含处理结果的字典
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 预处理
        preprocessed = self.preprocess_image(image)
        
        # 分割
        binary_mask, contours = self.segment_cells(preprocessed)
        
        # 过滤轮廓
        filtered_contours = self.filter_contours(contours)
        
        # 可视化结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        result_image = self.visualize_results(
            image, filtered_contours, 
            os.path.join(output_dir, f"{base_name}_segmented.jpg")
        )
        
        # 保存二值化掩码
        cv2.imwrite(os.path.join(output_dir, f"{base_name}_mask.jpg"), binary_mask)
        
        return {
            "original_image": image,
            "preprocessed_image": preprocessed,
            "binary_mask": binary_mask,
            "contours": filtered_contours,
            "result_image": result_image,
            "cell_count": len(filtered_contours)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="红细胞分割算法")
    parser.add_argument("--input", "-i", type=str, help="输入图像路径或目录")
    parser.add_argument("--output", "-o", type=str, default="output", help="输出目录")
    parser.add_argument("--min_area", type=int, default=100, help="最小细胞面积")
    parser.add_argument("--max_area", type=int, default=5000, help="最大细胞面积")
    parser.add_argument("--show", action="store_true", help="显示结果")
    
    args = parser.parse_args()
    
    # 创建分割器
    segmenter = RedBloodCellSegmentation(min_area=args.min_area, max_area=args.max_area)
    
    if args.input:
        if os.path.isfile(args.input):
            # 处理单个文件
            result = segmenter.process_image(args.input, args.output)
            print(f"检测到 {result['cell_count']} 个红细胞")
            
            if args.show:
                plt.figure(figsize=(15, 5))
                plt.subplot(1, 3, 1)
                plt.imshow(cv2.cvtColor(result['original_image'], cv2.COLOR_BGR2RGB))
                plt.title("原始图像")
                plt.axis('off')
                
                plt.subplot(1, 3, 2)
                plt.imshow(result['binary_mask'], cmap='gray')
                plt.title("二值化掩码")
                plt.axis('off')
                
                plt.subplot(1, 3, 3)
                plt.imshow(cv2.cvtColor(result['result_image'], cv2.COLOR_BGR2RGB))
                plt.title(f"分割结果 ({result['cell_count']} 个细胞)")
                plt.axis('off')
                
                plt.tight_layout()
                plt.show()
        else:
            print("请提供有效的图像文件路径")
    else:
        # 处理当前目录下的所有BMP文件
        bmp_files = [f for f in os.listdir('.') if f.lower().endswith('.bmp')]
        if bmp_files:
            total_cells = 0
            for bmp_file in bmp_files:
                print(f"处理图像: {bmp_file}")
                result = segmenter.process_image(bmp_file, args.output)
                cell_count = result['cell_count']
                total_cells += cell_count
                print(f"  检测到 {cell_count} 个红细胞")
            
            print(f"\n总计检测到 {total_cells} 个红细胞")
        else:
            print("当前目录下没有找到BMP图像文件")


if __name__ == "__main__":
    main()
