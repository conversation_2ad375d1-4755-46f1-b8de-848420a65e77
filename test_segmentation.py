#!/usr/bin/env python3
"""
红细胞分割算法测试脚本
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from red_blood_cell_segmentation import RedBloodCellSegmentation


def test_segmentation():
    """测试分割算法"""
    print("开始测试红细胞分割算法...")
    
    # 创建分割器实例
    segmenter = RedBloodCellSegmentation(min_area=50, max_area=8000)
    
    # 查找当前目录下的BMP文件
    bmp_files = [f for f in os.listdir('.') if f.lower().endswith('.bmp')]
    
    if not bmp_files:
        print("错误: 当前目录下没有找到BMP图像文件")
        return False
    
    print(f"找到 {len(bmp_files)} 个BMP文件: {bmp_files}")
    
    # 创建输出目录
    output_dir = "segmentation_results"
    os.makedirs(output_dir, exist_ok=True)
    
    total_cells = 0
    successful_files = 0
    
    for bmp_file in bmp_files:
        try:
            print(f"\n处理图像: {bmp_file}")
            
            # 处理图像
            result = segmenter.process_image(bmp_file, output_dir)
            cell_count = result['cell_count']
            total_cells += cell_count
            successful_files += 1
            
            print(f"  ✓ 成功检测到 {cell_count} 个红细胞")
            print(f"  ✓ 结果已保存到 {output_dir}/")
            
            # 显示处理结果的统计信息
            original_shape = result['original_image'].shape
            print(f"  - 图像尺寸: {original_shape[1]}x{original_shape[0]}")
            
            # 计算细胞密度
            image_area = original_shape[0] * original_shape[1]
            density = cell_count / (image_area / 1000000)  # 每平方毫米的细胞数（假设分辨率）
            print(f"  - 细胞密度: {density:.2f} 个/平方毫米 (估算)")
            
        except Exception as e:
            print(f"  ✗ 处理 {bmp_file} 时出错: {str(e)}")
            continue
    
    # 输出总结
    print(f"\n{'='*50}")
    print(f"测试完成!")
    print(f"成功处理: {successful_files}/{len(bmp_files)} 个文件")
    print(f"总计检测到: {total_cells} 个红细胞")
    print(f"结果保存在: {output_dir}/ 目录")
    print(f"{'='*50}")
    
    return successful_files > 0


def visualize_sample_result():
    """可视化一个样本的处理结果"""
    bmp_files = [f for f in os.listdir('.') if f.lower().endswith('.bmp')]
    
    if not bmp_files:
        print("没有找到BMP文件进行可视化")
        return
    
    # 选择第一个文件进行详细可视化
    sample_file = bmp_files[0]
    print(f"\n详细可视化处理过程: {sample_file}")
    
    segmenter = RedBloodCellSegmentation(min_area=50, max_area=8000)
    
    # 读取图像
    image = cv2.imread(sample_file)
    if image is None:
        print(f"无法读取图像: {sample_file}")
        return
    
    # 执行各个处理步骤
    preprocessed = segmenter.preprocess_image(image)
    binary_mask, contours = segmenter.segment_cells(preprocessed)
    filtered_contours = segmenter.filter_contours(contours)
    result_image = segmenter.visualize_results(image, filtered_contours)
    
    # 创建可视化图表
    plt.figure(figsize=(20, 12))
    
    # 原始图像
    plt.subplot(2, 3, 1)
    plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    plt.title(f"原始图像\n{sample_file}", fontsize=12)
    plt.axis('off')
    
    # 预处理后的图像
    plt.subplot(2, 3, 2)
    plt.imshow(preprocessed, cmap='gray')
    plt.title("预处理后\n(去噪+对比度增强)", fontsize=12)
    plt.axis('off')
    
    # 二值化掩码
    plt.subplot(2, 3, 3)
    plt.imshow(binary_mask, cmap='gray')
    plt.title("二值化掩码\n(形态学处理)", fontsize=12)
    plt.axis('off')
    
    # 轮廓检测结果
    contour_image = image.copy()
    cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 1)
    plt.subplot(2, 3, 4)
    plt.imshow(cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB))
    plt.title(f"所有轮廓\n({len(contours)} 个)", fontsize=12)
    plt.axis('off')
    
    # 过滤后的轮廓
    filtered_image = image.copy()
    cv2.drawContours(filtered_image, filtered_contours, -1, (0, 255, 0), 2)
    plt.subplot(2, 3, 5)
    plt.imshow(cv2.cvtColor(filtered_image, cv2.COLOR_BGR2RGB))
    plt.title(f"过滤后轮廓\n({len(filtered_contours)} 个)", fontsize=12)
    plt.axis('off')
    
    # 最终结果
    plt.subplot(2, 3, 6)
    plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    plt.title(f"最终分割结果\n{len(filtered_contours)} 个红细胞", fontsize=12)
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('segmentation_process_visualization.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存为: segmentation_process_visualization.png")


def main():
    """主测试函数"""
    print("红细胞分割算法测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import cv2
        import numpy as np
        import matplotlib.pyplot as plt
        print("✓ 所有依赖库已正确安装")
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 运行基本测试
    success = test_segmentation()
    
    if success:
        # 询问是否显示详细可视化
        try:
            response = input("\n是否显示详细的处理过程可视化? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                visualize_sample_result()
        except KeyboardInterrupt:
            print("\n测试结束")
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
