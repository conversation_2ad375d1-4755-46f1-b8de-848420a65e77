#!/usr/bin/env python3
"""
改进的红细胞分割算法
基于分析结果重新设计，针对低对比度血液细胞图像优化
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import os
import argparse
from scipy import ndimage
from skimage import filters, morphology, segmentation, measure

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Hiragino Sans GB']
plt.rcParams['axes.unicode_minus'] = False


class ImprovedRBCSegmentation:
    """改进的红细胞分割类"""
    
    def __init__(self, min_area: int = 800, max_area: int = 4000, 
                 min_circularity: float = 0.6, max_eccentricity: float = 0.8):
        """
        初始化改进的分割器
        
        Args:
            min_area: 最小细胞面积阈值（增加以过滤噪声）
            max_area: 最大细胞面积阈值
            min_circularity: 最小圆形度阈值
            max_eccentricity: 最大偏心率阈值
        """
        self.min_area = min_area
        self.max_area = max_area
        self.min_circularity = min_circularity
        self.max_eccentricity = max_eccentricity
        
    def enhanced_preprocessing(self, image: np.ndarray) -> np.ndarray:
        """
        增强的图像预处理
        
        Args:
            image: 输入的BGR图像
            
        Returns:
            预处理后的图像
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 1. 背景减除 - 使用大核的形态学开运算估计背景
        kernel_bg = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (50, 50))
        background = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel_bg)
        
        # 减去背景
        gray_corrected = cv2.subtract(gray, background)
        gray_corrected = cv2.add(gray_corrected, 50)  # 增加基础亮度
        
        # 2. 对比度增强 - 使用CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray_corrected)
        
        # 3. 高斯滤波去噪
        blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        return blurred
    
    def advanced_segmentation(self, preprocessed_image: np.ndarray) -> Tuple[np.ndarray, List]:
        """
        高级分割算法
        
        Args:
            preprocessed_image: 预处理后的图像
            
        Returns:
            分割掩码和轮廓列表
        """
        # 1. 使用Otsu阈值进行初始分割
        _, binary_otsu = cv2.threshold(preprocessed_image, 0, 255, 
                                      cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 2. 形态学操作清理
        # 去除小噪声
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        cleaned = cv2.morphologyEx(binary_otsu, cv2.MORPH_OPEN, kernel_small, iterations=2)
        
        # 填充细胞内部
        kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        filled = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_fill, iterations=3)
        
        # 3. 距离变换和分水岭分离重叠细胞
        dist_transform = cv2.distanceTransform(filled, cv2.DIST_L2, 5)

        # 寻找局部最大值作为种子点
        # 使用阈值找到确定的前景
        _, sure_fg = cv2.threshold(dist_transform, 0.5 * dist_transform.max(), 255, 0)
        sure_fg = np.uint8(sure_fg)

        # 使用连通组件标记
        _, markers = cv2.connectedComponents(sure_fg)

        # 分水岭分割
        labels = segmentation.watershed(-dist_transform, markers, mask=filled)
        
        # 4. 提取轮廓
        contours = []
        for region in measure.regionprops(labels):
            if region.area > 0:  # 跳过背景
                # 获取轮廓坐标
                coords = region.coords
                # 转换为OpenCV格式的轮廓
                contour = np.array([[coord[1], coord[0]] for coord in coords], dtype=np.int32)
                # 计算凸包作为轮廓
                hull = cv2.convexHull(contour)
                contours.append(hull)
        
        # 创建最终的二值掩码
        final_mask = np.zeros_like(filled)
        for i, contour in enumerate(contours):
            cv2.fillPoly(final_mask, [contour], 255)
        
        return final_mask, contours
    
    def intelligent_filtering(self, contours: List, image_shape: Tuple) -> List:
        """
        智能轮廓过滤
        
        Args:
            contours: 输入轮廓列表
            image_shape: 图像形状
            
        Returns:
            过滤后的轮廓列表
        """
        filtered_contours = []
        
        for contour in contours:
            # 基本几何特征
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            
            # 面积过滤
            if not (self.min_area <= area <= self.max_area):
                continue
            
            # 周长检查（避免除零）
            if perimeter == 0:
                continue
            
            # 圆形度计算
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            
            # 圆形度过滤
            if circularity < self.min_circularity:
                continue
            
            # 椭圆拟合检查偏心率
            if len(contour) >= 5:  # 椭圆拟合至少需要5个点
                try:
                    ellipse = cv2.fitEllipse(contour)
                    # 计算偏心率
                    a, b = ellipse[1][0]/2, ellipse[1][1]/2  # 长短轴
                    if a > 0 and b > 0:
                        eccentricity = np.sqrt(1 - (min(a,b)/max(a,b))**2)
                        if eccentricity > self.max_eccentricity:
                            continue
                except:
                    continue
            
            # 边界检查 - 排除接触图像边界的轮廓
            x, y, w, h = cv2.boundingRect(contour)
            margin = 10
            if (x < margin or y < margin or 
                x + w > image_shape[1] - margin or 
                y + h > image_shape[0] - margin):
                continue
            
            # 长宽比检查
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio > 2.0:  # 红细胞应该接近圆形
                continue
            
            filtered_contours.append(contour)
        
        return filtered_contours
    
    def visualize_results(self, original_image: np.ndarray, contours: List, 
                         save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化分割结果
        """
        result_image = original_image.copy()
        
        # 绘制轮廓和编号
        for i, contour in enumerate(contours):
            # 绘制轮廓
            cv2.drawContours(result_image, [contour], -1, (0, 255, 0), 2)
            
            # 计算质心并标注编号
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result_image, str(i+1), (cx-10, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        
        # 添加统计信息
        cell_count = len(contours)
        cv2.putText(result_image, f"RBC Count: {cell_count}", (10, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
        cv2.putText(result_image, f"RBC Count: {cell_count}", (10, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 2)
        
        if save_path:
            cv2.imwrite(save_path, result_image)
            
        return result_image
    
    def process_image(self, image_path: str, output_dir: str = "improved_output") -> dict:
        """
        处理单张图像的完整流程
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        print(f"处理图像: {image_path}")
        print(f"图像尺寸: {image.shape}")
        
        # 预处理
        preprocessed = self.enhanced_preprocessing(image)
        
        # 分割
        binary_mask, contours = self.advanced_segmentation(preprocessed)
        
        # 智能过滤
        filtered_contours = self.intelligent_filtering(contours, image.shape[:2])
        
        print(f"初始轮廓数: {len(contours)}")
        print(f"过滤后轮廓数: {len(filtered_contours)}")
        
        # 可视化结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        result_image = self.visualize_results(
            image, filtered_contours, 
            os.path.join(output_dir, f"{base_name}_improved_segmented.jpg")
        )
        
        # 保存中间结果
        cv2.imwrite(os.path.join(output_dir, f"{base_name}_preprocessed.jpg"), preprocessed)
        cv2.imwrite(os.path.join(output_dir, f"{base_name}_improved_mask.jpg"), binary_mask)
        
        return {
            "original_image": image,
            "preprocessed_image": preprocessed,
            "binary_mask": binary_mask,
            "contours": filtered_contours,
            "result_image": result_image,
            "cell_count": len(filtered_contours)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="改进的红细胞分割算法")
    parser.add_argument("--input", "-i", type=str, help="输入图像路径")
    parser.add_argument("--output", "-o", type=str, default="improved_output", help="输出目录")
    parser.add_argument("--min_area", type=int, default=800, help="最小细胞面积")
    parser.add_argument("--max_area", type=int, default=4000, help="最大细胞面积")
    parser.add_argument("--show", action="store_true", help="显示结果")
    
    args = parser.parse_args()
    
    # 创建改进的分割器
    segmenter = ImprovedRBCSegmentation(
        min_area=args.min_area, 
        max_area=args.max_area
    )
    
    if args.input:
        if os.path.isfile(args.input):
            result = segmenter.process_image(args.input, args.output)
            print(f"检测到 {result['cell_count']} 个红细胞")
            
            if args.show:
                plt.figure(figsize=(15, 5))
                plt.subplot(1, 3, 1)
                plt.imshow(cv2.cvtColor(result['original_image'], cv2.COLOR_BGR2RGB))
                plt.title("Original Image")
                plt.axis('off')
                
                plt.subplot(1, 3, 2)
                plt.imshow(result['preprocessed_image'], cmap='gray')
                plt.title("Enhanced Preprocessing")
                plt.axis('off')
                
                plt.subplot(1, 3, 3)
                plt.imshow(cv2.cvtColor(result['result_image'], cv2.COLOR_BGR2RGB))
                plt.title(f"Improved Result ({result['cell_count']} cells)")
                plt.axis('off')
                
                plt.tight_layout()
                plt.show()
    else:
        # 处理当前目录下的所有BMP文件
        bmp_files = [f for f in os.listdir('.') if f.lower().endswith('.bmp')]
        if bmp_files:
            total_cells = 0
            for bmp_file in bmp_files:
                result = segmenter.process_image(bmp_file, args.output)
                cell_count = result['cell_count']
                total_cells += cell_count
                print(f"文件 {bmp_file}: {cell_count} 个红细胞")
            
            print(f"\n总计检测到 {total_cells} 个红细胞")
        else:
            print("当前目录下没有找到BMP图像文件")


if __name__ == "__main__":
    main()
